<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Upload Image to Cloudinary</title>
</head>
<body>
  <h2>Upload Image</h2>
  <form id="uploadForm" enctype="multipart/form-data">
    <input type="text" name="folder">
    <input type="file" name="image" accept="image/*" required />
    <button type="submit">Upload</button>
  </form>

  <div id="result"></div>

  <script>
    const form = document.getElementById('uploadForm');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);

      try {
        const response = await fetch('https://ecommerce-server-3iw9.onrender.com/api/utils/store/image/upload', {
          method: 'POST',
          body: formData
        });

        const data = await response.json();

        if (data.url) {
          resultDiv.innerHTML = `<p>✅ Upload thành công!</p><img src="${data.url}" width="300" />`;
        } else {
          resultDiv.innerHTML = `<p style="color:red;">❌ Lỗi: ${data.error}</p>`;
        }
      } catch (err) {
        resultDiv.innerHTML = `<p style="color:red;">❌ Lỗi kết nối: ${err.message}</p>`;
      }
    });
  </script>
</body>
</html>