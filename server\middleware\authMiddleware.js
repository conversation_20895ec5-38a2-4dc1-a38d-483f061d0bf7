const jwt = require('jsonwebtoken');
const { User } = require('../models');

// <PERSON><PERSON><PERSON> thực <PERSON>
const authenticateToken = async (req, res, next) => {
    // Lấy token từ header Authorization: Bearer TOKEN
    const authHeader = req.headers['authorization'];
    let token = null;

    if (authHeader) {
        // Kiểm tra định dạng của Authorization header
        if (authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7); // Lấy phần sau 'Bearer '
        } else {
            // Nếu không có 'Bearer ' prefix, sử dụng toàn bộ giá trị
            token = authHeader;
        }
    }

    // Nếu không tìm thấy token trong header, kiểm tra trong cookies
    if (!token) {
        token = req.cookies && req.cookies.accessToken;
    }

    if (!token) {
        // Không có token -> <PERSON><PERSON>a đăng nhập
        return res.status(401).json({
            success: false,
            error: '<PERSON><PERSON><PERSON> cầu chưa được x<PERSON>c thực (<PERSON>hông tìm thấy token).'
        });
    }

    try {
        // <PERSON>á<PERSON> thực token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Token hợp lệ, tìm thông tin user tương ứng
        const user = await User.findByPk(decoded.userId, {
            // Loại bỏ password_hash
             attributes: { exclude: ['password_hash'] }
        });
        // Xóa console.log để tránh log thông tin nhạy cảm
        if (!user) {
            // User không tồn tại (có thể đã bị xóa sau khi token được tạo)
            return res.status(403).json({
                success: false,
                error: 'Token hợp lệ nhưng không tìm thấy người dùng.'
            });
        }

        // Gắn thông tin user vào request để các middleware/handler sau có thể sử dụng
        req.user = {
            userId: user.user_id,
            user_id: user.user_id, // Add user_id for backward compatibility
            username: user.username,
            email: user.email,
            role: user.role
        };

        next(); // Chuyển sang middleware hoặc route handler tiếp theo

    } catch (error) {
        console.error("Lỗi xác thực token:", error.name, error.message);
        if (error.name === 'TokenExpiredError') {
            return res.status(403).json({
                success: false,
                error: 'Token đã hết hạn.',
                refresh: true
            });
        }
        if (error.name === 'JsonWebTokenError') {
            return res.status(403).json({
                success: false,
                error: `Token không hợp lệ: ${error.message}`
            });
        }
        // Lỗi khác
        return res.status(500).json({
            success: false,
            error: 'Lỗi hệ thống khi xác thực token.'
        });
    }
};

// 2. Middleware phân quyền dựa trên vai trò (Role)
//    Đây là một factory function: nhận vào danh sách các role được phép
// Trả về middleware function
const authorizeRole = (allowedRoles) => {
    return (req, res, next) => {
        // Middleware này phải chạy SAU authenticateToken, nên req.user đã có
        if (!req.user || !req.user.role) {
             // Trường hợp bất thường nếu authenticateToken lỗi hoặc user không có role
            return res.status(403).json({ error: 'Không thể xác định vai trò người dùng.' });
        }

        const userRole = req.user.role;

        // Kiểm tra quyền người dùng
        if (allowedRoles && Array.isArray(allowedRoles) && allowedRoles.includes(userRole)) {
            next(); // Role hợp lệ, cho phép tiếp tục
        } else {
            // Role không hợp lệ
            res.status(403).json({ error: 'Bạn không có quyền truy cập tài nguyên này.' });
        }
    };
};


module.exports = {
    authenticateToken,
    authorizeRole
};