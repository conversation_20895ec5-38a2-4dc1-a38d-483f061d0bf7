server {
    listen 80;
    server_name localhost your_domain.com; # Thay bằng domain thực tế

    client_max_body_size 50M; # <PERSON><PERSON><PERSON><PERSON> hạn upload file (điều chỉnh nếu cần)

    location / {
        # Proxy đến tên service 'backend' và cổng được expose bởi backend (3434)
        proxy_pass http://backend:3434; # <---- THAY ĐỔI QUAN TRỌNG

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
}