{"name": "btl-server", "version": "0.1.0", "private": true, "scripts": {"start": "nodemon --inspect app.js"}, "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cloudinary": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "path": "^0.12.7", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "sanitize-html": "^2.16.0", "sequelize": "^6.37.6", "slugify": "^1.6.6"}, "main": "app.js", "author": "", "license": "ISC", "description": "", "devDependencies": {"nodemon": "^3.1.9"}}